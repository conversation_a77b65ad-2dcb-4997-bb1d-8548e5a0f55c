import cv2
import numpy as np
import matplotlib.pyplot as plt
import os
from square_decomposition import SquareDecomposer

def create_test_image():
    """创建一个测试图像，包含重叠的正方形"""
    # 创建白色背景
    img = np.ones((400, 400, 3), dtype=np.uint8) * 255
    
    # 绘制几个重叠的黑色正方形
    # 正方形1: 大正方形
    cv2.rectangle(img, (50, 50), (200, 200), (0, 0, 0), 3)
    
    # 正方形2: 中等正方形，部分重叠
    cv2.rectangle(img, (150, 100), (250, 200), (0, 0, 0), 3)
    
    # 正方形3: 小正方形，旋转45度
    center = (300, 150)
    size = 60
    # 计算旋转后的四个角点
    angle = 45 * np.pi / 180
    cos_a, sin_a = np.cos(angle), np.sin(angle)
    
    # 正方形的四个角点（相对于中心）
    half_size = size // 2
    corners = [
        (-half_size, -half_size),
        (half_size, -half_size),
        (half_size, half_size),
        (-half_size, half_size)
    ]
    
    # 旋转并平移到实际位置
    rotated_corners = []
    for x, y in corners:
        new_x = int(center[0] + x * cos_a - y * sin_a)
        new_y = int(center[1] + x * sin_a + y * cos_a)
        rotated_corners.append((new_x, new_y))
    
    # 绘制旋转的正方形
    pts = np.array(rotated_corners, np.int32)
    cv2.polylines(img, [pts], True, (0, 0, 0), 3)
    
    # 正方形4: 另一个小正方形
    cv2.rectangle(img, (80, 250), (150, 320), (0, 0, 0), 3)
    
    return img

def test_with_custom_image(img_path):
    """测试自定义图像"""
    if not os.path.exists(img_path):
        print(f"图像文件不存在: {img_path}")
        return False
    
    print(f"\n=== 测试图像: {img_path} ===")
    
    # 创建分解器
    decomposer = SquareDecomposer()
    
    try:
        # 处理图像
        result_img, corners, squares = decomposer.decompose(img_path)
        
        # 显示结果
        fig, axes = plt.subplots(1, 2, figsize=(15, 7))
        
        # 原图
        original = cv2.imread(img_path)
        axes[0].imshow(cv2.cvtColor(original, cv2.COLOR_BGR2RGB))
        axes[0].set_title('原始图像')
        axes[0].axis('off')
        
        # 结果图
        axes[1].imshow(cv2.cvtColor(result_img, cv2.COLOR_BGR2RGB))
        axes[1].set_title(f'分解结果 - 找到 {len(squares)} 个正方形')
        axes[1].axis('off')
        
        plt.tight_layout()
        plt.show()
        
        # 保存结果
        result_filename = f"result_{os.path.basename(img_path)}"
        cv2.imwrite(result_filename, result_img)
        print(f"结果已保存为: {result_filename}")
        
        # 打印详细信息
        print(f"\n检测结果:")
        print(f"- 角点数量: {len(corners)}")
        print(f"- 正方形数量: {len(squares)}")
        
        for i, square in enumerate(squares):
            center = np.mean(square, axis=0)
            side_length = np.mean([
                np.linalg.norm(square[j] - square[(j+1)%4]) 
                for j in range(4)
            ])
            print(f"  正方形 {i+1}: 中心({center[0]:.1f}, {center[1]:.1f}), 边长{side_length:.1f}")
        
        return True
        
    except Exception as e:
        print(f"处理出错: {e}")
        return False

def test_with_generated_image():
    """测试生成的图像"""
    print("\n=== 测试生成的图像 ===")
    
    # 创建测试图像
    test_img = create_test_image()
    cv2.imwrite("test_generated.jpg", test_img)
    
    # 测试
    return test_with_custom_image("test_generated.jpg")

def main():
    """主测试函数"""
    print("正方形分解算法测试")
    print("=" * 50)
    
    # 测试1: 生成的图像
    success1 = test_with_generated_image()
    
    # 测试2: 用户提供的图像
    # 请将你的图像文件放在当前目录下，并修改文件名
    user_images = [
        "test_image.jpg",      # 请替换为你的图像文件名
        "polygon_image.png",   # 可以添加更多测试图像
        "complex_shape.jpg"
    ]
    
    success_count = 0
    total_tests = 1  # 生成图像测试
    
    for img_path in user_images:
        if os.path.exists(img_path):
            total_tests += 1
            if test_with_custom_image(img_path):
                success_count += 1
        else:
            print(f"\n跳过不存在的图像: {img_path}")
    
    if success1:
        success_count += 1
    
    print(f"\n" + "=" * 50)
    print(f"测试完成: {success_count}/{total_tests} 个测试成功")
    
    if success_count == 0:
        print("\n建议:")
        print("1. 确保图像文件存在且路径正确")
        print("2. 检查图像是否包含清晰的正方形边缘")
        print("3. 调整算法参数以适应你的图像特征")

if __name__ == "__main__":
    main()
