import cv2
import numpy as np
import matplotlib.pyplot as plt
from sklearn.cluster import DBSCAN
import math

class PolygonDecomposer:
    def __init__(self):
        self.min_square_size = 20
        self.max_square_size = 500
        self.angle_tolerance = 15
        self.line_merge_threshold = 10
        
    def preprocess_image(self, img_path):
        """图像预处理"""
        img = cv2.imread(img_path)
        if img is None:
            raise ValueError(f"无法读取图像: {img_path}")
        
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # 二值化
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # 如果黑色区域是目标，确保目标为白色
        if np.mean(binary) > 127:
            binary = cv2.bitwise_not(binary)
        
        # 形态学操作清理噪声
        kernel = np.ones((3,3), np.uint8)
        binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
        binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        
        # 边缘检测
        edges = cv2.Canny(binary, 50, 150)
        
        return img, gray, binary, edges
    
    def detect_lines(self, edges):
        """检测直线段"""
        # 使用概率霍夫变换检测直线
        lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=50,
                               minLineLength=30, maxLineGap=10)
        
        if lines is None:
            return []
        
        # 转换格式并过滤短线段
        line_segments = []
        for line in lines:
            x1, y1, x2, y2 = line[0]
            length = np.sqrt((x2-x1)**2 + (y2-y1)**2)
            if length > 20:  # 过滤太短的线段
                line_segments.append([x1, y1, x2, y2])
        
        return line_segments
    
    def classify_lines(self, lines):
        """将直线分类为水平线和垂直线"""
        horizontal_lines = []
        vertical_lines = []
        
        for line in lines:
            x1, y1, x2, y2 = line
            
            # 计算角度
            if x2 - x1 == 0:
                angle = 90
            else:
                angle = abs(math.degrees(math.atan((y2 - y1) / (x2 - x1))))
            
            # 分类
            if angle < self.angle_tolerance:  # 水平线
                horizontal_lines.append(line)
            elif abs(angle - 90) < self.angle_tolerance:  # 垂直线
                vertical_lines.append(line)
        
        return horizontal_lines, vertical_lines
    
    def merge_collinear_lines(self, lines, is_horizontal=True):
        """合并共线的线段"""
        if not lines:
            return []
        
        merged_lines = []
        
        for line in lines:
            x1, y1, x2, y2 = line
            
            # 确保线段方向一致
            if is_horizontal:
                if x1 > x2:
                    x1, y1, x2, y2 = x2, y2, x1, y1
                key_coord = (y1 + y2) // 2  # 水平线的y坐标
            else:
                if y1 > y2:
                    x1, y1, x2, y2 = x2, y2, x1, y1
                key_coord = (x1 + x2) // 2  # 垂直线的x坐标
            
            # 查找可以合并的线段
            merged = False
            for i, merged_line in enumerate(merged_lines):
                mx1, my1, mx2, my2 = merged_line
                
                if is_horizontal:
                    merged_key = (my1 + my2) // 2
                    if abs(key_coord - merged_key) < self.line_merge_threshold:
                        # 合并水平线段
                        new_x1 = min(x1, mx1)
                        new_x2 = max(x2, mx2)
                        new_y = (key_coord + merged_key) // 2
                        merged_lines[i] = [new_x1, new_y, new_x2, new_y]
                        merged = True
                        break
                else:
                    merged_key = (mx1 + mx2) // 2
                    if abs(key_coord - merged_key) < self.line_merge_threshold:
                        # 合并垂直线段
                        new_y1 = min(y1, my1)
                        new_y2 = max(y2, my2)
                        new_x = (key_coord + merged_key) // 2
                        merged_lines[i] = [new_x, new_y1, new_x, new_y2]
                        merged = True
                        break
            
            if not merged:
                merged_lines.append([x1, y1, x2, y2])
        
        return merged_lines
    
    def find_line_intersections(self, h_lines, v_lines):
        """找到水平线和垂直线的交点"""
        intersections = []
        
        for h_line in h_lines:
            hx1, hy1, hx2, hy2 = h_line
            h_y = (hy1 + hy2) // 2
            
            for v_line in v_lines:
                vx1, vy1, vx2, vy2 = v_line
                v_x = (vx1 + vx2) // 2
                
                # 检查交点是否在两条线段的范围内
                if (min(hx1, hx2) <= v_x <= max(hx1, hx2) and
                    min(vy1, vy2) <= h_y <= max(vy1, vy2)):
                    intersections.append([v_x, h_y])
        
        return intersections
    
    def find_rectangles_from_intersections(self, intersections):
        """从交点中找到矩形"""
        if len(intersections) < 4:
            return []
        
        rectangles = []
        intersections = np.array(intersections)
        
        # 尝试所有4点组合
        from itertools import combinations
        for combo in combinations(range(len(intersections)), 4):
            points = intersections[list(combo)]
            
            if self.is_valid_rectangle(points):
                # 按顺序排列点
                ordered_points = self.order_rectangle_points(points)
                rectangles.append(ordered_points)
        
        return rectangles
    
    def is_valid_rectangle(self, points):
        """检查四个点是否构成矩形"""
        if len(points) != 4:
            return False
        
        # 计算所有边长
        distances = []
        for i in range(len(points)):
            for j in range(i+1, len(points)):
                dist = np.linalg.norm(points[i] - points[j])
                distances.append(dist)
        
        distances.sort()
        
        # 矩形应该有4条边和2条对角线
        # 4条边中应该有两对相等的边
        sides = distances[:4]
        diagonals = distances[4:]
        
        # 检查是否有两对相等的边
        side_pairs = []
        used = [False] * 4
        
        for i in range(4):
            if used[i]:
                continue
            for j in range(i+1, 4):
                if used[j]:
                    continue
                if abs(sides[i] - sides[j]) / max(sides[i], sides[j]) < 0.1:
                    side_pairs.append((sides[i], sides[j]))
                    used[i] = used[j] = True
                    break
        
        if len(side_pairs) != 2:
            return False
        
        # 检查对角线是否相等
        if len(diagonals) >= 2:
            if abs(diagonals[0] - diagonals[1]) / max(diagonals[0], diagonals[1]) > 0.1:
                return False
        
        # 检查尺寸范围
        min_side = min(sides)
        max_side = max(sides)
        
        if min_side < self.min_square_size or max_side > self.max_square_size:
            return False
        
        return True
    
    def order_rectangle_points(self, points):
        """按顺时针顺序排列矩形的点"""
        center = np.mean(points, axis=0)
        
        # 计算每个点相对于中心的角度
        angles = []
        for point in points:
            angle = math.atan2(point[1] - center[1], point[0] - center[0])
            angles.append(angle)
        
        # 按角度排序
        sorted_indices = np.argsort(angles)
        return points[sorted_indices]
    
    def visualize_results(self, img, h_lines, v_lines, intersections, rectangles):
        """可视化结果"""
        result_img = img.copy()
        
        # 绘制水平线（红色）
        for line in h_lines:
            x1, y1, x2, y2 = line
            cv2.line(result_img, (x1, y1), (x2, y2), (0, 0, 255), 2)
        
        # 绘制垂直线（蓝色）
        for line in v_lines:
            x1, y1, x2, y2 = line
            cv2.line(result_img, (x1, y1), (x2, y2), (255, 0, 0), 2)
        
        # 绘制交点（绿色）
        for point in intersections:
            cv2.circle(result_img, tuple(map(int, point)), 5, (0, 255, 0), -1)
        
        # 绘制矩形
        colors = [(255, 255, 0), (255, 0, 255), (0, 255, 255), (128, 0, 128), 
                 (255, 165, 0), (0, 128, 0), (128, 128, 0), (128, 0, 128)]
        
        for i, rect in enumerate(rectangles):
            color = colors[i % len(colors)]
            
            # 绘制矩形边框
            pts = rect.astype(np.int32)
            cv2.polylines(result_img, [pts], True, color, 4)
            
            # 标注
            center = np.mean(rect, axis=0).astype(int)
            cv2.putText(result_img, f'R{i+1}', tuple(center), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1.0, color, 2)
        
        return result_img
    
    def decompose(self, img_path):
        """主分解函数"""
        print(f"开始分解多边形: {img_path}")
        
        # 预处理
        img, gray, binary, edges = self.preprocess_image(img_path)
        
        # 检测直线
        lines = self.detect_lines(edges)
        print(f"检测到 {len(lines)} 条直线段")
        
        if not lines:
            print("未检测到足够的直线段")
            return img, [], [], []
        
        # 分类直线
        h_lines, v_lines = self.classify_lines(lines)
        print(f"水平线: {len(h_lines)}, 垂直线: {len(v_lines)}")
        
        # 合并共线的线段
        h_lines = self.merge_collinear_lines(h_lines, True)
        v_lines = self.merge_collinear_lines(v_lines, False)
        print(f"合并后 - 水平线: {len(h_lines)}, 垂直线: {len(v_lines)}")
        
        # 找交点
        intersections = self.find_line_intersections(h_lines, v_lines)
        print(f"找到 {len(intersections)} 个交点")
        
        # 从交点构建矩形
        rectangles = self.find_rectangles_from_intersections(intersections)
        print(f"识别出 {len(rectangles)} 个矩形")
        
        # 可视化
        result_img = self.visualize_results(img, h_lines, v_lines, intersections, rectangles)
        
        return result_img, h_lines, v_lines, rectangles

def main():
    decomposer = PolygonDecomposer()
    
    # 查找图像文件
    import os
    image_files = [f for f in os.listdir('.') if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))]
    
    if not image_files:
        print("没有找到图像文件")
        return
    
    for img_file in image_files:
        try:
            print(f"\n{'='*50}")
            result_img, h_lines, v_lines, rectangles = decomposer.decompose(img_file)
            
            # 显示结果
            plt.figure(figsize=(15, 5))
            
            plt.subplot(1, 3, 1)
            original = cv2.imread(img_file)
            plt.imshow(cv2.cvtColor(original, cv2.COLOR_BGR2RGB))
            plt.title('原图')
            plt.axis('off')
            
            plt.subplot(1, 3, 2)
            _, _, binary, _ = decomposer.preprocess_image(img_file)
            plt.imshow(binary, cmap='gray')
            plt.title('二值化')
            plt.axis('off')
            
            plt.subplot(1, 3, 3)
            plt.imshow(cv2.cvtColor(result_img, cv2.COLOR_BGR2RGB))
            plt.title(f'分解结果\n{len(rectangles)} 个矩形')
            plt.axis('off')
            
            plt.tight_layout()
            plt.show()
            
            # 保存结果
            cv2.imwrite(f'decomposed_{img_file}', result_img)
            print(f"✓ 结果保存为: decomposed_{img_file}")
            
        except Exception as e:
            print(f"处理 {img_file} 时出错: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    main()
