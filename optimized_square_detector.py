import cv2
import numpy as np
import matplotlib.pyplot as plt
from itertools import combinations
import math

class OptimizedSquareDetector:
    def __init__(self):
        # 优化后的参数，避免计算爆炸
        self.corner_quality = 0.01      # 提高质量阈值，减少角点数量
        self.corner_min_distance = 15   # 增加最小距离，减少密集角点
        self.angle_tolerance = 20       # 角度容差
        self.side_length_tolerance = 0.3 # 边长容差
        self.min_square_size = 30       # 最小正方形尺寸
        self.max_square_size = 800      # 最大正方形尺寸
        self.max_corners = 100          # 限制最大角点数量
        self.max_combinations = 20000   # 限制最大组合数量
        
    def preprocess_image_focused(self, img_path):
        """针对复杂多边形的预处理"""
        img = cv2.imread(img_path)
        if img is None:
            raise ValueError(f"无法读取图像: {img_path}")
        
        # 转换为灰度图
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # 使用OTSU自动阈值
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # 反转二值图（如果需要）
        if np.mean(binary) > 127:  # 如果白色像素更多，反转
            binary = cv2.bitwise_not(binary)
        
        # 形态学操作，清理噪声
        kernel = np.ones((3,3), np.uint8)
        binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
        binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        
        # 边缘检测
        edges = cv2.Canny(binary, 50, 150)
        
        return img, gray, binary, edges
    
    def detect_contour_corners(self, binary):
        """基于轮廓的角点检测"""
        # 找到轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        corners = []
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if area < 500:  # 过滤小轮廓
                continue
            
            # 多边形近似
            epsilon = 0.01 * cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, epsilon, True)
            
            # 提取角点
            for point in approx:
                corners.append(point[0])
        
        return np.array(corners) if corners else np.array([])
    
    def detect_corners_smart(self, gray, binary, edges):
        """智能角点检测，限制数量"""
        corners_list = []
        
        # 方法1: 轮廓角点（主要方法）
        contour_corners = self.detect_contour_corners(binary)
        if len(contour_corners) > 0:
            corners_list.extend(contour_corners)
        
        # 方法2: Harris角点检测（限制数量）
        harris_corners = cv2.goodFeaturesToTrack(
            gray, 
            maxCorners=self.max_corners,
            qualityLevel=self.corner_quality,
            minDistance=self.corner_min_distance,
            useHarrisDetector=True
        )
        
        if harris_corners is not None:
            corners_list.extend(harris_corners.reshape(-1, 2))
        
        if not corners_list:
            return np.array([])
        
        # 去重
        corners = np.array(corners_list)
        corners = self.remove_duplicate_corners(corners)
        
        # 如果角点太多，选择最强的角点
        if len(corners) > self.max_corners:
            # 计算角点强度
            corner_responses = []
            for corner in corners:
                x, y = int(corner[0]), int(corner[1])
                if 0 <= x < gray.shape[1] and 0 <= y < gray.shape[0]:
                    # 使用Harris响应作为强度
                    response = cv2.cornerHarris(gray[max(0,y-5):y+5, max(0,x-5):x+5], 2, 3, 0.04)
                    corner_responses.append(np.max(response))
                else:
                    corner_responses.append(0)
            
            # 选择响应最强的角点
            top_indices = np.argsort(corner_responses)[-self.max_corners:]
            corners = corners[top_indices]
        
        return corners
    
    def remove_duplicate_corners(self, corners):
        """去除重复角点"""
        if len(corners) == 0:
            return corners
        
        unique_corners = []
        for corner in corners:
            is_duplicate = False
            for existing in unique_corners:
                if np.linalg.norm(corner - existing) < self.corner_min_distance:
                    is_duplicate = True
                    break
            if not is_duplicate:
                unique_corners.append(corner)
        
        return np.array(unique_corners)
    
    def is_valid_square(self, points):
        """验证四个点是否构成正方形"""
        if len(points) != 4:
            return False
        
        # 计算边长
        sides = []
        for i in range(4):
            p1 = points[i]
            p2 = points[(i + 1) % 4]
            side_length = np.linalg.norm(p1 - p2)
            sides.append(side_length)
        
        # 检查边长范围
        avg_side = np.mean(sides)
        if avg_side < self.min_square_size or avg_side > self.max_square_size:
            return False
        
        # 检查边长相等性
        for side in sides:
            if abs(side - avg_side) / avg_side > self.side_length_tolerance:
                return False
        
        # 检查角度
        angles = []
        for i in range(4):
            p1 = points[(i - 1) % 4]
            p2 = points[i]
            p3 = points[(i + 1) % 4]
            
            v1 = p1 - p2
            v2 = p3 - p2
            
            cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
            cos_angle = np.clip(cos_angle, -1, 1)
            angle = np.arccos(cos_angle) * 180 / np.pi
            angles.append(angle)
        
        # 检查角度接近90度
        for angle in angles:
            if abs(angle - 90) > self.angle_tolerance:
                return False
        
        return True
    
    def find_squares_efficient(self, corners):
        """高效的正方形查找"""
        squares = []
        
        if len(corners) < 4:
            return squares
        
        print(f"从 {len(corners)} 个角点中寻找正方形...")
        
        # 限制组合数量
        total_combinations = len(list(combinations(range(len(corners)), 4)))
        if total_combinations > self.max_combinations:
            print(f"组合数量过多 ({total_combinations})，使用启发式搜索...")
            return self.find_squares_heuristic(corners)
        
        # 暴力搜索（数量较少时）
        count = 0
        for combo in combinations(range(len(corners)), 4):
            count += 1
            if count % 5000 == 0:
                print(f"已检查 {count}/{total_combinations} 个组合")
            
            four_points = corners[list(combo)]
            
            if self.is_valid_square(four_points):
                ordered_points = self.order_points(four_points)
                squares.append(ordered_points)
                print(f"找到正方形 #{len(squares)}")
        
        return squares
    
    def find_squares_heuristic(self, corners):
        """启发式正方形搜索"""
        squares = []
        
        # 按距离分组角点
        for i, corner in enumerate(corners):
            # 找到距离合适的邻近点
            neighbors = []
            for j, other in enumerate(corners):
                if i != j:
                    dist = np.linalg.norm(corner - other)
                    if self.min_square_size <= dist <= self.max_square_size * 1.5:
                        neighbors.append((j, other, dist))
            
            # 如果邻近点足够，尝试构建正方形
            if len(neighbors) >= 3:
                # 按距离排序，优先考虑距离相近的点
                neighbors.sort(key=lambda x: x[2])
                
                # 限制搜索范围
                max_neighbors = min(len(neighbors), 15)
                for combo in combinations(neighbors[:max_neighbors], 3):
                    four_points = np.array([corner] + [pt[1] for pt in combo])
                    
                    if self.is_valid_square(four_points):
                        ordered_points = self.order_points(four_points)
                        squares.append(ordered_points)
                        print(f"找到正方形 #{len(squares)}")
        
        return squares
    
    def order_points(self, points):
        """按顺时针顺序排列点"""
        center = np.mean(points, axis=0)
        angles = []
        for point in points:
            angle = math.atan2(point[1] - center[1], point[0] - center[0])
            angles.append(angle)
        
        sorted_indices = np.argsort(angles)
        return points[sorted_indices]
    
    def remove_duplicate_squares(self, squares):
        """去除重复正方形"""
        if len(squares) <= 1:
            return squares
        
        unique_squares = []
        for square in squares:
            is_duplicate = False
            center = np.mean(square, axis=0)
            avg_side = np.mean([np.linalg.norm(square[i] - square[(i+1)%4]) for i in range(4)])
            
            for existing in unique_squares:
                existing_center = np.mean(existing, axis=0)
                existing_side = np.mean([np.linalg.norm(existing[i] - existing[(i+1)%4]) for i in range(4)])
                
                # 检查中心距离和尺寸相似性
                center_dist = np.linalg.norm(center - existing_center)
                size_diff = abs(avg_side - existing_side) / max(avg_side, existing_side)
                
                if center_dist < avg_side * 0.3 and size_diff < 0.2:
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                unique_squares.append(square)
        
        return unique_squares
    
    def visualize_results(self, img, corners, squares):
        """可视化结果"""
        result_img = img.copy()
        
        # 绘制角点
        for corner in corners:
            cv2.circle(result_img, tuple(corner.astype(int)), 4, (0, 255, 0), -1)
        
        # 绘制正方形
        colors = [(255, 0, 0), (0, 0, 255), (255, 255, 0), (255, 0, 255), 
                 (0, 255, 255), (128, 0, 128), (255, 165, 0), (0, 128, 0)]
        
        for i, square in enumerate(squares):
            color = colors[i % len(colors)]
            
            # 绘制正方形边框
            pts = square.astype(np.int32)
            cv2.polylines(result_img, [pts], True, color, 5)
            
            # 绘制角点
            for point in square:
                cv2.circle(result_img, tuple(point.astype(int)), 8, color, -1)
            
            # 标注
            center = np.mean(square, axis=0).astype(int)
            cv2.putText(result_img, f'S{i+1}', tuple(center), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1.2, color, 3)
        
        return result_img
    
    def detect_squares(self, img_path):
        """主检测函数"""
        print(f"开始处理: {img_path}")
        
        # 预处理
        img, gray, binary, edges = self.preprocess_image_focused(img_path)
        
        # 智能角点检测
        corners = self.detect_corners_smart(gray, binary, edges)
        print(f"检测到 {len(corners)} 个角点")
        
        if len(corners) < 4:
            print("角点数量不足")
            return img, corners, []
        
        # 高效正方形搜索
        squares = self.find_squares_efficient(corners)
        print(f"找到 {len(squares)} 个候选正方形")
        
        # 去重
        unique_squares = self.remove_duplicate_squares(squares)
        print(f"去重后剩余 {len(unique_squares)} 个正方形")
        
        # 可视化
        result_img = self.visualize_results(img, corners, unique_squares)
        
        return result_img, corners, unique_squares

def main():
    """主函数"""
    detector = OptimizedSquareDetector()
    
    # 查找图像文件
    import os
    image_files = [f for f in os.listdir('.') if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))]
    
    if not image_files:
        print("没有找到图像文件")
        return
    
    for img_file in image_files:
        try:
            print(f"\n{'='*50}")
            result_img, corners, squares = detector.detect_squares(img_file)
            
            # 显示结果
            plt.figure(figsize=(15, 5))
            
            plt.subplot(1, 3, 1)
            original = cv2.imread(img_file)
            plt.imshow(cv2.cvtColor(original, cv2.COLOR_BGR2RGB))
            plt.title('原图')
            plt.axis('off')
            
            plt.subplot(1, 3, 2)
            _, _, binary, _ = detector.preprocess_image_focused(img_file)
            plt.imshow(binary, cmap='gray')
            plt.title(f'二值化\n{len(corners)} 个角点')
            plt.axis('off')
            
            plt.subplot(1, 3, 3)
            plt.imshow(cv2.cvtColor(result_img, cv2.COLOR_BGR2RGB))
            plt.title(f'检测结果\n{len(squares)} 个正方形')
            plt.axis('off')
            
            plt.tight_layout()
            plt.show()
            
            # 保存结果
            cv2.imwrite(f'optimized_result_{img_file}', result_img)
            print(f"✓ 结果保存为: optimized_result_{img_file}")
            
            # 详细信息
            if squares:
                print("\n正方形详情:")
                for i, square in enumerate(squares):
                    center = np.mean(square, axis=0)
                    avg_side = np.mean([np.linalg.norm(square[j] - square[(j+1)%4]) for j in range(4)])
                    print(f"  正方形 {i+1}: 中心({center[0]:.1f}, {center[1]:.1f}), 边长{avg_side:.1f}")
            
        except Exception as e:
            print(f"处理 {img_file} 时出错: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    main()
